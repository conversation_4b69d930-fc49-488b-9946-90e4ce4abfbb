{"name": "vertoie-web", "version": "0.1.0", "description": "Vertoie web platform - AI-powered business application platform", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint src", "lint:fix": "eslint src --fix", "format": "prettier --write src", "type-check": "tsc --noEmit"}, "dependencies": {"@solidjs/router": "^0.10.0", "solid-js": "^1.8.0"}, "devDependencies": {"@eslint/js": "^9.32.0", "@solidjs/testing-library": "^0.8.0", "@testing-library/jest-dom": "^6.6.3", "@types/node": "^20.0.0", "@vitest/ui": "^3.2.4", "eslint": "^9.32.0", "eslint-plugin-solid": "^0.14.5", "globals": "^16.3.0", "jsdom": "^26.1.0", "prettier": "^3.0.0", "typescript": "^5.0.0", "typescript-eslint": "^8.38.0", "vite": "^5.0.0", "vite-plugin-solid": "^2.8.0", "vitest": "^3.2.4"}, "keywords": ["solidjs", "vite", "typescript", "business-applications", "ai-powered"], "author": "Vertoie Team", "license": "MIT"}