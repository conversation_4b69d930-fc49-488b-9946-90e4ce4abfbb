import { Component, createSignal, onMount } from 'solid-js';
import './Landing.css';

const Landing: Component = () => {
  const [pricingTiers, setPricingTiers] = createSignal([]);
  const [loading, setLoading] = createSignal(true);

  onMount(async () => {
    // Fetch pricing data
    try {
      const response = await fetch('/api/v1/pricing');
      if (response.ok) {
        const data = await response.json();
        setPricingTiers(data);
      } else {
        // Fallback pricing data
        setPricingTiers([
          {
            name: 'Starter',
            price_monthly: 4900,
            description: 'Perfect for small teams getting started',
            features: [
              { feature_name: '5 AI Applications' },
              { feature_name: '100 AI Conversations/month' },
              { feature_name: 'Web & Mobile Deployment' },
              { feature_name: 'Email Support' }
            ]
          },
          {
            name: 'Professional',
            price_monthly: 19900,
            description: 'Most popular for established businesses',
            is_featured: true,
            features: [
              { feature_name: 'Unlimited AI Applications' },
              { feature_name: '2,000 AI Conversations/month' },
              { feature_name: 'Advanced Analytics' },
              { feature_name: 'API Access' },
              { feature_name: 'White-label Solutions' },
              { feature_name: 'Dedicated Support' }
            ]
          },
          {
            name: 'Business',
            price_monthly: 39900,
            description: 'Enterprise-grade features and support',
            features: [
              { feature_name: 'Everything in Professional' },
              { feature_name: '10,000 AI Conversations/month' },
              { feature_name: 'Advanced Security' },
              { feature_name: 'Custom Integrations' },
              { feature_name: 'SLA Guarantee' },
              { feature_name: 'Account Manager' }
            ]
          }
        ]);
      }
    } catch (err) {
      console.error('Pricing fetch error:', err);
    } finally {
      setLoading(false);
    }

    // Navbar scroll effect
    const handleScroll = () => {
      const navbar = document.getElementById('navbar');
      if (navbar) {
        if (window.scrollY > 50) {
          navbar.classList.add('scrolled');
        } else {
          navbar.classList.remove('scrolled');
        }
      }
    };
    window.addEventListener('scroll', handleScroll);

    // Fade in animation on scroll
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
        }
      });
    }, observerOptions);

    document.querySelectorAll('.fade-in').forEach(el => {
      observer.observe(el);
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Demo typing effect
    const demoTexts = [
      '> "I need a customer management system"',
      '> "Create an inventory tracking app"',
      '> "Build a project management tool"',
      '> "I want a sales dashboard"'
    ];
    let currentIndex = 0;

    const demoInterval = setInterval(() => {
      const typingElement = document.querySelector('.typing-text');
      if (typingElement) {
        currentIndex = (currentIndex + 1) % demoTexts.length;
        typingElement.textContent = demoTexts[currentIndex];
      }
    }, 4000);

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
      observer.disconnect();
      clearInterval(demoInterval);
    };
  });

  const formatPrice = (cents: number) => {
    return `$${(cents / 100).toFixed(0)}`;
  };

  return (
    <div class="landing-page">
      {/* Navigation */}
      <nav id="navbar">
        <div class="nav-container">
          <svg class="logo" viewBox="0 23.1 161.11 37.540001" width="161.11" height="37.540001">
            <defs>
              <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" style="stop-color:#FF6B35"/>
                <stop offset="100%" style="stop-color:#F7931E"/>
              </linearGradient>
            </defs>
            <path d="m 0,33.86 h 6.62 l 6.22,19.73 6.15,-19.73 h 6.28 L 16.38,60.08 H 9.33 Z M 29.13,51.88 V 41.65 q 0,-4 3.04,-6.18 3.04,-2.17 8.14,-2.17 6.05,0 8.6,2.32 2.55,2.32 2.55,6.27 v 6.35 H 35.77 v 4.27 q 0,2.32 1.19,2.92 1.2,0.6 3.57,0.6 2.83,0 3.79,-0.79 0.97,-0.8 0.97,-3.21 h 6.15 q 0,4.85 -2.73,6.73 -2.74,1.88 -8.3,1.88 -3.2,0 -5.77,-0.79 -2.56,-0.79 -4.04,-2.7 -1.47,-1.9 -1.47,-5.27 m 16.06,-7.69 v -3.08 q 0,-1.46 -0.62,-2.13 -0.62,-0.67 -1.69,-0.86 -1.06,-0.18 -2.4,-0.18 -2,0 -3.36,0.6 -1.35,0.6 -1.35,2.7 v 2.95 z M 58.06,33.86 h 6.64 v 3.13 q 0.58,-1.03 2.06,-1.82 1.48,-0.79 3.38,-1.25 1.91,-0.45 3.81,-0.45 l -0.15,5.64 q -1.27,0.07 -2.76,0.32 -1.48,0.24 -2.86,0.66 -1.38,0.41 -2.34,1.02 -0.95,0.61 -1.14,1.39 V 60.08 H 58.06 Z M 79.47,51.61 V 38.09 H 76 v -4.23 h 3.66 l 0.61,-8.42 h 5.84 v 8.42 h 6.22 v 4.23 h -6.22 v 13.03 q 0,2.44 1.06,3.38 1.06,0.94 4.07,0.94 h 1.26 l -0.24,4.94 h -1.81 q -3.95,0 -6.37,-0.79 -2.41,-0.78 -3.51,-2.69 -1.1,-1.92 -1.1,-5.29 z m 29.2,9.03 q -6.2,0 -8.79,-2.29 -2.59,-2.3 -2.59,-6.2 V 41.82 q 0,-4 2.99,-6.26 2.99,-2.26 8.39,-2.26 5.37,0 8.37,2.26 3,2.26 3,6.26 v 10.33 q 0,2.59 -1.07,4.5 -1.07,1.92 -3.55,2.96 -2.48,1.03 -6.75,1.03 m 0,-4.61 q 2.12,0 3.43,-0.79 1.3,-0.8 1.3,-2.8 V 41.53 q 0,-2 -1.24,-2.8 -1.25,-0.79 -3.49,-0.79 -2.15,0 -3.45,0.79 -1.29,0.8 -1.29,2.8 v 10.91 q 0,2 1.22,2.8 1.22,0.79 3.52,0.79 z M 126.1,33.86 h 6.61 V 60.08 H 126.1 V 33.86 M 126.05,23.1 h 6.69 v 6.47 h -6.69 z m 12.72,28.78 V 41.65 q 0,-4 3.04,-6.18 3.04,-2.17 8.14,-2.17 6.06,0 8.61,2.32 2.55,2.32 2.55,6.27 v 6.35 h -15.7 v 4.27 q 0,2.32 1.2,2.92 1.19,0.6 3.56,0.6 2.83,0 3.8,-0.79 0.96,-0.8 0.96,-3.21 h 6.15 q 0,4.85 -2.73,6.73 -2.73,1.88 -8.3,1.88 -3.2,0 -5.76,-0.79 -2.57,-0.79 -4.04,-2.7 -1.48,-1.9 -1.48,-5.27 m 16.06,-7.69 v -3.08 q 0,-1.46 -0.62,-2.13 -0.62,-0.67 -1.68,-0.86 -1.07,-0.18 -2.41,-0.18 -2,0 -3.35,0.6 -1.36,0.6 -1.36,2.7 v 2.95 z" fill="url(#logoGradient)"/>
          </svg>
          
          <ul class="nav-links">
            <li><a href="#features">Features</a></li>
            <li><a href="#demo">How It Works</a></li>
            <li><a href="#pricing">Pricing</a></li>
            <li><a href="/app">Get Started</a></li>
          </ul>
        </div>
      </nav>

      {/* Hero Section */}
      <section class="hero">
        <div class="hero-container">
          <div class="hero-content">
            <h1>Transform Ideas into Applications with AI</h1>
            <p>Vertoie turns your business ideas into fully functional applications through natural conversation. No coding required. Deploy across web, desktop, and mobile instantly.</p>
            <div class="cta-buttons">
              <a href="/app" class="btn-primary">Start Building Free</a>
              <a href="#demo" class="btn-secondary">See How It Works</a>
            </div>
          </div>
          
          <div class="hero-demo">
            <div class="demo-screen">
              <div class="typing-text">> "I need a customer management system"</div>
              <br />
              <div style="color: #FBBF24;">🔄 Generating application...</div>
              <div style="color: #22C55E;">✅ Customer database</div>
              <div style="color: #22C55E;">✅ Contact management</div>
              <div style="color: #22C55E;">✅ Sales pipeline</div>
              <div style="color: #22C55E;">✅ Reporting dashboard</div>
              <br />
              <div style="color: #3B82F6;">💡 Your application is ready!</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section class="features" id="features">
        <div class="container">
          <div class="section-header fade-in">
            <h2>AI-Powered Application Development</h2>
            <p>Transform your business ideas into powerful applications through natural conversation. No technical expertise required.</p>
          </div>
          
          <div class="features-grid">
            <div class="feature-card fade-in">
              <div class="feature-icon">🤖</div>
              <h3>Conversational AI Development</h3>
              <p>Describe your application needs in plain English and watch as AI generates fully functional software tailored to your requirements.</p>
            </div>
            
            <div class="feature-card fade-in">
              <div class="feature-icon">🚀</div>
              <h3>Cross-Platform Deployment</h3>
              <p>Deploy your applications instantly across web, desktop, and mobile platforms with automatic scaling and monitoring.</p>
            </div>
            
            <div class="feature-card fade-in">
              <div class="feature-icon">🏢</div>
              <h3>Multi-Tenant Architecture</h3>
              <p>Built-in multi-tenancy support allows you to serve multiple customers with isolated data and customization.</p>
            </div>
            
            <div class="feature-card fade-in">
              <div class="feature-icon">⚡</div>
              <h3>Real-Time Collaboration</h3>
              <p>Work with your team in real-time to refine and enhance your applications through collaborative AI conversations.</p>
            </div>
            
            <div class="feature-card fade-in">
              <div class="feature-icon">📊</div>
              <h3>Advanced Analytics</h3>
              <p>Comprehensive analytics and insights to understand how your applications are performing and being used.</p>
            </div>
            
            <div class="feature-card fade-in">
              <div class="feature-icon">🔗</div>
              <h3>Seamless Integrations</h3>
              <p>Connect with popular business tools and services. Your existing workflow enhanced, not replaced.</p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section class="demo-section" id="demo">
        <div class="container">
          <div class="demo-container">
            <div class="demo-content fade-in">
              <h2>From Idea to Application in Minutes</h2>
              <p>Watch how business ideas transform into fully functional applications through simple conversation.</p>
              
              <div class="demo-steps">
                <div class="demo-step">
                  <div class="step-number">1</div>
                  <div>
                    <strong>Describe Your Idea</strong><br />
                    "I need a project management tool for my team"
                  </div>
                </div>
                
                <div class="demo-step">
                  <div class="step-number">2</div>
                  <div>
                    <strong>AI Generates Your App</strong><br />
                    Task management, team collaboration, progress tracking - instantly built
                  </div>
                </div>
                
                <div class="demo-step">
                  <div class="step-number">3</div>
                  <div>
                    <strong>Refine & Customize</strong><br />
                    Continue the conversation to add features and adjust functionality
                  </div>
                </div>
                
                <div class="demo-step">
                  <div class="step-number">4</div>
                  <div>
                    <strong>Deploy Everywhere</strong><br />
                    Launch on web, desktop, and mobile with automatic scaling
                  </div>
                </div>
              </div>
              
              <a href="/app" class="btn-primary">Start Building Now</a>
            </div>
            
            <div class="demo-visual fade-in">
              <div class="phone-mockup">
                <div class="phone-screen">
                  <div style="margin-bottom: 1rem;">
                    <strong>"Create a customer feedback system"</strong>
                  </div>
                  <div style="background: #F4F4F5; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                    <div style="color: #22C55E; margin-bottom: 0.5rem;">✅ Feedback forms</div>
                    <div style="color: #22C55E; margin-bottom: 0.5rem;">✅ Rating system</div>
                    <div style="color: #22C55E; margin-bottom: 0.5rem;">✅ Analytics dashboard</div>
                    <div style="color: #22C55E;">✅ Email notifications</div>
                  </div>
                  <div style="background: linear-gradient(135deg, #FF6B35, #F7931E); color: white; padding: 1rem; border-radius: 8px; text-align: center;">
                    <strong>Application Generated</strong><br />
                    Ready to deploy<br />
                    <small>Deploy to web, mobile & desktop</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section class="pricing" id="pricing">
        <div class="container">
          <div class="section-header fade-in">
            <h2>Simple, Transparent Pricing</h2>
            <p>Choose the plan that fits your needs. Start free and scale as you grow.</p>
          </div>
          
          {loading() ? (
            <div class="loading">Loading pricing...</div>
          ) : (
            <div class="pricing-grid">
              {pricingTiers().map((tier: any) => (
                <div class={`pricing-card ${tier.is_featured ? 'featured' : ''} fade-in`}>
                  <div class="plan-name">{tier.name}</div>
                  <div class="plan-price">
                    {formatPrice(tier.price_monthly)}
                    <span>/month</span>
                  </div>
                  <div class="plan-description">{tier.description}</div>
                  <ul class="plan-features">
                    {tier.features.map((feature: any) => (
                      <li>{feature.feature_name}</li>
                    ))}
                  </ul>
                  <a href="/app" class="btn-primary">Get Started</a>
                </div>
              ))}
            </div>
          )}
          
          <div style="text-align: center; margin-top: 3rem;">
            <p style="color: #52525B; font-size: 1.1rem;">
              <strong>Solo plan starting at $25/month</strong> • 
              <a href="/contact" style="color: #FF6B35; text-decoration: none; font-weight: 600;">Enterprise: Contact Sales</a>
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="cta-section">
        <div class="container">
          <h2>Ready to Transform Your Ideas?</h2>
          <p>Join thousands of businesses already using AI to build custom applications. Get started in minutes.</p>
          <div class="cta-buttons">
            <a href="/app" class="btn-primary">Start Free Trial</a>
            <a href="#demo" class="btn-secondary">Watch Demo</a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer>
        <div class="container">
          <div class="footer-content">
            <div class="footer-section">
              <h3>Product</h3>
              <a href="#features">Features</a>
              <a href="#pricing">Pricing</a>
              <a href="#demo">How It Works</a>
              <a href="/app">Get Started</a>
            </div>
            
            <div class="footer-section">
              <h3>Company</h3>
              <a href="/about">About Us</a>
              <a href="/contact">Contact</a>
              <a href="/careers">Careers</a>
              <a href="/blog">Blog</a>
            </div>
            
            <div class="footer-section">
              <h3>Resources</h3>
              <a href="/docs">Documentation</a>
              <a href="/help">Help Center</a>
              <a href="/community">Community</a>
              <a href="/api">API</a>
            </div>
            
            <div class="footer-section">
              <h3>Legal</h3>
              <a href="/privacy">Privacy Policy</a>
              <a href="/terms">Terms of Service</a>
              <a href="/security">Security</a>
            </div>
          </div>
          
          <div class="footer-bottom">
            <p>&copy; 2025 Vertoie. All rights reserved. Transform your ideas into applications with AI.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Landing;
