import { Component, createSignal, onMount } from 'solid-js';

interface ApiInfo {
  name: string;
  version: string;
  description: string;
  status: string;
}

const Home: Component = () => {
  const [apiInfo, setApiInfo] = createSignal<ApiInfo | null>(null);
  const [loading, setLoading] = createSignal(true);
  const [error, setError] = createSignal<string | null>(null);

  onMount(async () => {
    try {
      const response = await fetch('/api');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setApiInfo(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch API info');
    } finally {
      setLoading(false);
    }
  });

  return (
    <div class='page'>
      <div class='hero'>
        <h1>Welcome to Vertoie</h1>
        <p class='hero-subtitle'>
          Generate custom business applications through conversational AI and deploy them across
          multiple platforms.
        </p>
      </div>

      <div class='features'>
        <div class='feature-card'>
          <h3>🤖 AI-Powered Generation</h3>
          <p>
            Describe your business needs through natural conversation and watch as custom
            applications are generated automatically.
          </p>
        </div>

        <div class='feature-card'>
          <h3>🚀 Cross-Platform Deployment</h3>
          <p>
            Deploy your applications across web, desktop, and mobile platforms with a single click.
          </p>
        </div>

        <div class='feature-card'>
          <h3>⚡ Real-Time Collaboration</h3>
          <p>
            Iterate and refine your applications in real-time with AI assistance and team
            collaboration.
          </p>
        </div>
      </div>

      <div class='api-status'>
        <h3>Backend API Status</h3>
        {loading() && <p>Loading API information...</p>}
        {error() && <p class='error'>Error: {error()}</p>}
        {apiInfo() && (
          <div class='api-info'>
            <p>
              <strong>Name:</strong> {apiInfo()!.name}
            </p>
            <p>
              <strong>Version:</strong> {apiInfo()!.version}
            </p>
            <p>
              <strong>Status:</strong> <span class='status-badge'>{apiInfo()!.status}</span>
            </p>
            <p>
              <strong>Description:</strong> {apiInfo()!.description}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Home;
