import { render, screen } from '@solidjs/testing-library';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import Home from './Home';

// Mock fetch for testing
global.fetch = vi.fn();

describe('Home Component', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('renders welcome message', () => {
    render(() => <Home />);

    expect(screen.getByText('Welcome to Vertoie')).toBeInTheDocument();
  });

  it('renders hero subtitle', () => {
    render(() => <Home />);

    expect(screen.getByText(/Generate custom business applications through conversational AI/)).toBeInTheDocument();
  });

  it('renders feature cards', () => {
    render(() => <Home />);

    expect(screen.getByText('🤖 AI-Powered Generation')).toBeInTheDocument();
    expect(screen.getByText('🚀 Cross-Platform Deployment')).toBeInTheDocument();
    expect(screen.getByText('⚡ Real-Time Collaboration')).toBeInTheDocument();
  });

  it('shows loading state initially', () => {
    render(() => <Home />);

    expect(screen.getByText('Loading API information...')).toBeInTheDocument();
  });
});
