import { Component, createSignal, onMount } from 'solid-js';

interface HealthCheck {
  status: string;
  timestamp: string;
  version: string;
  checks: {
    server: string;
    database: string;
    database_message?: string;
  };
}

const Health: Component = () => {
  const [healthData, setHealthData] = createSignal<HealthCheck | null>(null);
  const [loading, setLoading] = createSignal(true);
  const [error, setError] = createSignal<string | null>(null);

  const fetchHealth = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/health');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setHealthData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch health data');
    } finally {
      setLoading(false);
    }
  };

  onMount(fetchHealth);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
      case 'ok':
        return 'green';
      case 'unhealthy':
      case 'error':
        return 'red';
      default:
        return 'orange';
    }
  };

  return (
    <div class='page'>
      <div class='health-header'>
        <h1>System Health</h1>
        <button onClick={fetchHealth} disabled={loading()}>
          {loading() ? 'Refreshing...' : 'Refresh'}
        </button>
      </div>

      {error() && (
        <div class='error-card'>
          <h3>❌ Connection Error</h3>
          <p>{error()}</p>
        </div>
      )}

      {healthData() && (
        <div class='health-dashboard'>
          <div class='health-overview'>
            <h2>Overall Status</h2>
            <div class={`status-indicator ${getStatusColor(healthData()!.status)}`}>
              {healthData()!.status.toUpperCase()}
            </div>
            <p class='timestamp'>
              Last checked: {new Date(healthData()!.timestamp).toLocaleString()}
            </p>
            <p class='version'>Version: {healthData()!.version}</p>
          </div>

          <div class='health-checks'>
            <h3>Component Health</h3>
            <div class='checks-grid'>
              <div class='check-item'>
                <span class='check-name'>Server</span>
                <span class={`check-status ${getStatusColor(healthData()!.checks.server)}`}>
                  {healthData()!.checks.server}
                </span>
              </div>

              <div class='check-item'>
                <span class='check-name'>Database</span>
                <span class={`check-status ${getStatusColor(healthData()!.checks.database)}`}>
                  {healthData()!.checks.database}
                </span>
                {healthData()!.checks.database_message && (
                  <span class='check-message'>{healthData()!.checks.database_message}</span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Health;
