.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background-color: #1a1a1a;
  padding: 1rem 2rem;
  border-bottom: 1px solid #333;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-brand h1 {
  margin: 0;
  font-size: 1.8rem;
  color: #646cff;
}

.nav-tagline {
  font-size: 0.9rem;
  color: #888;
  margin-left: 1rem;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  color: #fff;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.nav-link:hover {
  background-color: #333;
  color: #646cff;
}

.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
}

.app-footer {
  background-color: #1a1a1a;
  padding: 1rem 2rem;
  border-top: 1px solid #333;
  text-align: center;
  color: #888;
}

.page {
  text-align: left;
}

.hero {
  text-align: center;
  margin-bottom: 3rem;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #646cff, #535bf2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: #888;
  max-width: 600px;
  margin: 0 auto;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature-card {
  background-color: #2a2a2a;
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid #333;
}

.feature-card h3 {
  margin-top: 0;
  color: #646cff;
}

.api-status {
  background-color: #2a2a2a;
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid #333;
}

.api-info {
  text-align: left;
}

.status-badge {
  background-color: #4ade80;
  color: #000;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: bold;
  font-size: 0.8rem;
}

.error {
  color: #ef4444;
}

.health-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.health-dashboard {
  display: grid;
  gap: 2rem;
}

.health-overview {
  background-color: #2a2a2a;
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid #333;
  text-align: center;
}

.status-indicator {
  font-size: 2rem;
  font-weight: bold;
  padding: 1rem 2rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.status-indicator.green {
  background-color: #4ade80;
  color: #000;
}

.status-indicator.red {
  background-color: #ef4444;
  color: #fff;
}

.status-indicator.orange {
  background-color: #f59e0b;
  color: #000;
}

.timestamp {
  color: #888;
  font-size: 0.9rem;
}

.version {
  color: #888;
  font-size: 0.9rem;
}

.health-checks {
  background-color: #2a2a2a;
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid #333;
}

.checks-grid {
  display: grid;
  gap: 1rem;
}

.check-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #1a1a1a;
  border-radius: 4px;
  border: 1px solid #333;
}

.check-name {
  font-weight: 500;
}

.check-status {
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-weight: bold;
  font-size: 0.8rem;
  text-transform: uppercase;
}

.check-status.green {
  background-color: #4ade80;
  color: #000;
}

.check-status.red {
  background-color: #ef4444;
  color: #fff;
}

.check-status.orange {
  background-color: #f59e0b;
  color: #000;
}

.check-message {
  font-size: 0.8rem;
  color: #888;
  margin-left: 1rem;
}

.error-card {
  background-color: #2a1a1a;
  border: 1px solid #ef4444;
  padding: 2rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.error-card h3 {
  color: #ef4444;
  margin-top: 0;
}

@media (prefers-color-scheme: light) {
  .app-header,
  .app-footer {
    background-color: #f8f9fa;
    border-color: #e9ecef;
  }

  .nav-brand h1 {
    color: #646cff;
  }

  .nav-tagline {
    color: #6c757d;
  }

  .nav-link {
    color: #212529;
  }

  .nav-link:hover {
    background-color: #e9ecef;
    color: #646cff;
  }

  .app-footer {
    color: #6c757d;
  }

  .feature-card,
  .api-status,
  .health-overview,
  .health-checks {
    background-color: #f8f9fa;
    border-color: #e9ecef;
  }

  .check-item {
    background-color: #ffffff;
    border-color: #e9ecef;
  }

  .error-card {
    background-color: #fef2f2;
    border-color: #ef4444;
  }
}
