import { Component } from 'solid-js';
import { Route } from '@solidjs/router';
import Home from './pages/Home';
import Health from './pages/Health';
import Landing from './pages/Landing';
import './App.css';

const App: Component = () => {
  return (
    <div class='app'>
      <header class='app-header'>
        <nav class='nav'>
          <div class='nav-brand'>
            <h1>Vertoie</h1>
            <span class='nav-tagline'>AI-Powered Business Applications</span>
          </div>
          <div class='nav-links'>
            <a href='/' class='nav-link'>
              Home
            </a>
            <a href='/health' class='nav-link'>
              Health
            </a>
          </div>
        </nav>
      </header>

      <main class='main-content'>
        <Route path='/' component={Landing} />
        <Route path='/app' component={Home} />
        <Route path='/health' component={Health} />
      </main>

      <footer class='app-footer'>
        <p>&copy; 2025 Vertoie. AI-powered business application platform.</p>
      </footer>
    </div>
  );
};

export default App;
