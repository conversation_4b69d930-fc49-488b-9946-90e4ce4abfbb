---
type: "agent_requested"
description: "AI/LLM integration questions, Groq setup, business model generation, prompt engineering, real-time AI streaming, or LLM cost optimization"
---
# LLM Integration

## Strategy
1. **Groq** (0-500 users): Qwen3 32B, $60-120/month, 2-5s generation
2. **Hybrid** (500-2K): Smart caching + Groq, 50-70% cost reduction
3. **Self-hosted** (2K+): Own Qwen3 32B, 80-90% cost reduction

## Router
```rust
pub struct LLMRouter {
    groq_client: Option<GroqClient>,
    self_hosted_client: Option<SelfHostedClient>,
    use_groq: bool,
}

impl LLMRouter {
    pub async fn generate_business_model(
        &self,
        description: &str,
    ) -> Result<BusinessModel, LLMError> {
        if self.use_groq {
            self.groq_generate(description).await
        } else {
            self.self_hosted_generate(description).await
        }
    }
}
```

## Prompting
```rust
pub struct PromptBuilder {
    universal_patterns: String,
    industry_context: HashMap<String, String>,
}

impl PromptBuilder {
    pub fn build_prompt(&self, description: &str, industry: Option<&str>) -> String {
        let mut prompt = self.universal_patterns.clone();

        if let Some(industry) = industry {
            if let Some(context) = self.industry_context.get(industry) {
                prompt.push_str(context);
            }
        }

        prompt.push_str(&format!("\nBusiness: {}", description));
        prompt
    }
}
```

## Pipeline
```rust
pub struct AnalysisPipeline {
    llm_router: LLMRouter,
    industry_detector: IndustryDetector,
    module_selector: ModuleSelector,
}

impl AnalysisPipeline {
    pub async fn analyze(&self, conversation: &str) -> Result<BusinessAnalysis, Error> {
        let industry = self.industry_detector.detect(conversation).await?;
        let modules = self.module_selector.select(&industry).await?;
        let schemas = self.generate_schemas(&modules).await?;

        Ok(BusinessAnalysis { industry, modules, schemas })
    }
}
```

## WebSocket Streaming
```rust
pub async fn handle_ai_generation(ctx: &WebSocketContext, msg: &WebSocketMessage) -> Result<(), WebSocketError> {
    let channel = ctx.create_channel("ai-generation").await?;

    ctx.send_message(WebSocketMessage {
        id: msg.id.clone(),
        message_type: "ai.generation.started".to_string(),
        payload: json!({"generation_channel": channel.id}),
    }).await?;

    tokio::spawn(async move {
        for progress in (0..=100).step_by(10) {
            ctx.send_to_channel(&channel.id, WebSocketMessage {
                message_type: "ai.generation.progress".to_string(),
                payload: json!({"progress": progress}),
            }).await;
        }
    });

    Ok(())
}
```

## Generation Types
```rust
pub enum GenerationType {
    Standard,    // 1 credit
    Custom,      // 2 credits
}
```

## Error Handling
```rust
#[derive(Debug, thiserror::Error)]
pub enum LLMError {
    #[error("API failed: {0}")]
    ApiError(String),
    #[error("Rate limited")]
    RateLimited,
    #[error("Invalid response")]
    InvalidResponse,
}

// Fallback: retry on rate limit, switch provider on unavailable
```

## Optimization
- Cache popular templates
- Track success rates
- Monitor costs
- Usage analytics
