---
type: "agent_requested"
description: "Code style questions, Rust/TypeScript/SolidJS patterns, error handling, naming conventions, file organization, or code quality standards"
---
# Coding Standards

## Rust
- snake_case functions/variables
- `pub(crate)` for internal APIs
- Custom error types with thiserror
- Repository pattern for DB
- SQLx with compile-time checks

```rust
#[derive(Debug, thiserror::Error)]
pub enum ApiError {
    #[error("Database: {0}")]
    Database(#[from] sqlx::Error),
    #[error("Unauthorized")]
    Unauthorized,
}

pub type ApiResult<T> = Result<T, ApiError>;

#[derive(sqlx::FromRow)]
pub struct BusinessProfile {
    pub id: Uuid,
    pub name: String,
}

// WebSocket message
#[derive(Serialize, Deserialize)]
pub struct WebSocketMessage {
    pub id: String,
    pub message_type: String,
    pub payload: Value,
}
```

## TypeScript/SolidJS
- PascalCase components
- createStore for global state
- Platform-agnostic API client

```typescript
const BusinessProfile: Component<Props> = (props) => (
  <div class="business-profile">{props.children}</div>
);

const [store, setStore] = createStore({
  profile: null,
  loading: false,
});

class ApiClient {
  static async request<T>(endpoint: string): Promise<T> {
    return PlatformAdapter.isWeb()
      ? fetch(endpoint).then(r => r.json())
      : invoke('api_request', {endpoint});
  }
}
```

## Standards
- Files: `snake_case.rs`, `kebab-case.ts`, `PascalCase.tsx`
- Use `.env` for config
- Structured logging with tracing
- Result types for errors
- Document public APIs
