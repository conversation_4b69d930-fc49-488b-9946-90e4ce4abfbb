---
type: "agent_requested"
description: "Testing strategies, unit/integration/e2e test patterns, test setup, mocking, CI/CD testing, or test automation for Rust/TypeScript"
---
# Testing Guidelines

## Rust Testing
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_business_creation() {
        let pool = setup_test_db().await;
        let repo = BusinessRepository::new(pool);

        let business_data = CreateBusinessRequest {
            name: "Test Business".to_string(),
            industry: "Technology".to_string(),
        };

        let result = repo.create_business(business_data).await;

        assert!(result.is_ok());
        assert_eq!(result.unwrap().name, "Test Business");
    }

    async fn setup_test_db() -> PgPool {
        let url = std::env::var("TEST_DATABASE_URL").expect("TEST_DATABASE_URL required");
        PgPool::connect(&url).await.expect("DB connection failed")
    }
}
```

## Integration Testing
```rust
use axum_test::TestServer;
use serde_json::json;

#[tokio::test]
async fn test_business_api() {
    let app = create_app().await;
    let server = TestServer::new(app).unwrap();

    let response = server
        .post("/api/v1/businesses")
        .json(&json!({"name": "Test Business", "industry": "Tech"}))
        .await;

    assert_eq!(response.status_code(), StatusCode::CREATED);
    let business: BusinessProfile = response.json();
    assert_eq!(business.name, "Test Business");
}

#[tokio::test]
async fn test_websocket() {
    let server = TestServer::new(create_app().await).unwrap();
    let mut ws = server.get_websocket("/ws").await;

    ws.send_text(json!({"id": "test", "type": "voice.command.process"}).to_string()).await;
    let response = ws.recv_text().await.unwrap();
    let msg: serde_json::Value = serde_json::from_str(&response).unwrap();

    assert_eq!(msg["id"], "test");
}
```

## Mock Testing
```rust
use mockall::mock;

mock! {
    LLMClient {}

    #[async_trait]
    impl LLMClientTrait for LLMClient {
        async fn generate_business_model(&self, description: &str) -> Result<BusinessModel, LLMError>;
    }
}

#[tokio::test]
async fn test_with_mock_llm() {
    let mut mock_llm = MockLLMClient::new();

    mock_llm
        .expect_generate_business_model()
        .with(eq("Pool service"))
        .returning(|_| Ok(BusinessModel {
            industry: "Pool Service".to_string(),
            modules: vec!["customer_management", "scheduling"],
        }));

    let analyzer = BusinessAnalyzer::new(mock_llm);
    let result = analyzer.analyze("Pool service").await;

    assert!(result.is_ok());
    assert_eq!(result.unwrap().industry, "Pool Service");
}
```

## TypeScript Testing
```typescript
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@solidjs/testing-library';

describe('BusinessProfile', () => {
  const mockBusiness = { id: '123', name: 'Test Business', industry: 'Tech' };

  it('renders business info', () => {
    render(() => <BusinessProfile business={mockBusiness} />);
    expect(screen.getByText('Test Business')).toBeInTheDocument();
  });

  it('calls onUpdate on submit', () => {
    const onUpdate = vi.fn();
    render(() => <BusinessProfile business={mockBusiness} onUpdate={onUpdate} editable />);

    fireEvent.input(screen.getByLabelText('Name'), { target: { value: 'Updated' } });
    fireEvent.click(screen.getByText('Save'));

    expect(onUpdate).toHaveBeenCalledWith({ ...mockBusiness, name: 'Updated' });
  });
});
```

## Store Testing
```typescript
import { describe, it, expect, beforeEach } from 'vitest';

describe('businessStore', () => {
  beforeEach(() => {
    setBusinessStore({ profile: null, modules: [], loading: false });
  });

  it('initializes with defaults', () => {
    expect(businessStore.profile).toBeNull();
    expect(businessStore.modules).toEqual([]);
  });

  it('updates profile', () => {
    const profile = { id: '123', name: 'Test Business' };
    businessActions.updateProfile(profile);
    expect(businessStore.profile).toEqual(profile);
  });
});
```

## API Testing
```typescript
import { describe, it, expect, vi } from 'vitest';

global.fetch = vi.fn();
vi.mock('@tauri-apps/api/tauri', () => ({ invoke: vi.fn() }));

describe('ApiClient', () => {
  it('makes web request when not Tauri', async () => {
    const mockResponse = { id: '123', name: 'Test' };
    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse),
    });

    const result = await ApiClient.getBusinessProfile('123');

    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('/businesses/123'),
      expect.objectContaining({ method: 'GET' })
    );
    expect(result).toEqual(mockResponse);
  });
});
```

## E2E Testing
```typescript
// playwright.config.ts
export default defineConfig({
  testDir: './e2e',
  use: { baseURL: 'http://localhost:3000' },
  projects: [{ name: 'chromium', use: devices['Desktop Chrome'] }],
  webServer: { command: 'npm run dev', url: 'http://localhost:3000' },
});

// e2e/business-setup.spec.ts
import { test, expect } from '@playwright/test';

test('creates business through conversation', async ({ page }) => {
  await page.goto('/');
  await page.click('[data-testid="start-setup"]');
  await page.fill('[data-testid="business-description"]', 'Pool cleaning service');
  await page.click('[data-testid="analyze-business"]');

  await expect(page.locator('[data-testid="analysis-result"]')).toBeVisible();
  await expect(page.locator('text=Customer Management')).toBeVisible();

  await page.click('[data-testid="approve-modules"]');
  await expect(page.locator('[data-testid="generation-complete"]')).toBeVisible();
  await expect(page).toHaveURL(/\/dashboard/);
});
```

## Test Database
```sql
-- Test setup
CREATE SCHEMA test_vertoie;
CREATE SCHEMA test_business_123;

INSERT INTO test_vertoie.organizations (id, name, industry)
VALUES ('123e4567-e89b-12d3-a456-************', 'Test Business', 'Tech');
```

```rust
// Test utilities
pub struct TestDb {
    pub pool: PgPool,
}

impl TestDb {
    pub async fn new() -> Self {
        let url = std::env::var("TEST_DATABASE_URL").expect("TEST_DATABASE_URL required");
        let pool = PgPool::connect(&url).await.expect("DB connection failed");
        Self { pool }
    }

    pub async fn setup_org(&self, org_id: Uuid) -> Result<(), sqlx::Error> {
        sqlx::query(&format!("CREATE SCHEMA business_{}", org_id))
            .execute(&self.pool).await?;
        Ok(())
    }
}
```

## CI/CD
```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: vertoie_test

    steps:
      - uses: actions/checkout@v3
      - uses: actions-rs/toolchain@v1
      - run: cd core && cargo test
      - run: cd web && npm test
      - run: npx playwright test
```
