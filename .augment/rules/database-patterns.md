---
type: "agent_requested"
description: "Database design questions, PostgreSQL schemas, JSONB patterns, data migration, repository patterns, or multi-tenant data architecture"
---

# Database Patterns

**IMPORTANT** All database migrations should be transactions BEGIN ... COMMIT;

## Multi-Schema

- `vertoie`: Platform data (users, orgs, plans, usage)
- `business_{org_id}`: Business data + versioning

```sql
-- Platform schema
CREATE SCHEMA vertoie;

CREATE TABLE vertoie.users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE vertoie.organizations (
    id UUID PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    industry VARCHAR(100),
    settings JSONB DEFAULT '{}'
);

CREATE TABLE vertoie.subscriptions (
    id UUID PRIMARY KEY,
    organization_id UUID REFERENCES vertoie.organizations(id),
    plan_id UUID,
    status VARCHAR(50),
    current_period_start TIMESTAMP,
    current_period_end TIMESTAMP
);
```

## Business Schema

```sql
-- Per-organization schema
CREATE SCHEMA business_{org_id};

CREATE TABLE business_{org_id}.application_versions (
    id UUID PRIMARY KEY,
    version_number INTEGER UNIQUE,
    version_type VARCHAR(50), -- stable, development, historical
    status VARCHAR(50),
    description TEXT
);

CREATE TABLE business_{org_id}.business_data (
    id UUID PRIMARY KEY,
    data_type VARCHAR(100), -- customer, invoice, etc.
    data JSONB NOT NULL,
    version_id UUID REFERENCES business_{org_id}.application_versions(id),
    created_at TIMESTAMP DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT false
);

-- Indexes
CREATE INDEX idx_data_type ON business_{org_id}.business_data(data_type);
CREATE INDEX idx_data_jsonb ON business_{org_id}.business_data USING GIN(data);
```

## JSONB Patterns

```rust
pub struct SchemaValidator {
    schemas: HashMap<String, JSONSchema>,
}

impl SchemaValidator {
    pub fn validate(&self, schema_name: &str, data: &Value) -> Result<(), Vec<ValidationError>> {
        let schema = self.schemas.get(schema_name)?;
        match schema.validate(data) {
            Ok(_) => Ok(()),
            Err(errors) => Err(errors.collect()),
        }
    }
}

#[derive(Serialize, Deserialize)]
pub struct BusinessDataRecord {
    pub id: Uuid,
    pub data_type: String,
    pub data: Value,
    pub version_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub is_deleted: bool,
}
```

## Repository

```rust
pub struct BusinessDataRepository {
    pool: PgPool,
    organization_id: Uuid,
    validator: SchemaValidator,
}

impl BusinessDataRepository {
    pub async fn create(&self, data_type: &str, data: Value) -> Result<BusinessDataRecord, DatabaseError> {
        self.validator.validate(data_type, &data)?;

        let schema_name = format!("business_{}", self.organization_id);
        let query = format!("INSERT INTO {}.business_data (data_type, data) VALUES ($1, $2) RETURNING *", schema_name);

        sqlx::query_as!(
            BusinessDataRecord,
            &query,
            data_type, data
        )
        .fetch_one(&self.pool)
        .await
    }

    pub async fn find_by_type(&self, data_type: &str) -> Result<Vec<BusinessDataRecord>, DatabaseError> {
        let schema_name = format!("business_{}", self.organization_id);
        let query = format!("SELECT * FROM {}.business_data WHERE data_type = $1 AND is_deleted = false", schema_name);

        sqlx::query_as!(
            BusinessDataRecord,
            &query,
            data_type
        )
        .fetch_all(&self.pool)
        .await
    }

    pub async fn soft_delete(&self, id: Uuid) -> Result<(), DatabaseError> {
        let schema_name = format!("business_{}", self.organization_id);
        let query = format!("UPDATE {}.business_data SET is_deleted = true WHERE id = $1", schema_name);

        sqlx::query!(&query, id)
            .execute(&self.pool)
            .await?;
        Ok(())
    }
}
```

## Migration

```rust
pub struct SchemaMigrator {
    pool: PgPool,
    organization_id: Uuid,
}

impl SchemaMigrator {
    pub async fn migrate(&self, from: Uuid, to: Uuid) -> Result<MigrationResult, MigrationError> {
        let mut tx = self.pool.begin().await?;

        let backup_id = self.create_backup(&mut tx, from).await?;

        match self.execute_migration(&mut tx).await {
            Ok(_) => {
                tx.commit().await?;
                Ok(MigrationResult { success: true, backup_id })
            }
            Err(e) => {
                tx.rollback().await?;
                Err(e)
            }
        }
    }
}
```

## Performance

```sql
-- JSONB indexes
CREATE INDEX idx_customer_email ON business_{org_id}.business_data
USING GIN((data->>'email')) WHERE data_type = 'customer';

-- Composite indexes
CREATE INDEX idx_type_created ON business_{org_id}.business_data
(data_type, created_at DESC);

-- Partial indexes
CREATE INDEX idx_active ON business_{org_id}.business_data
(data_type) WHERE is_deleted = false;
```
