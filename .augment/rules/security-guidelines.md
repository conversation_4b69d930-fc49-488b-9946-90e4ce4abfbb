---
type: "agent_requested"
description: "Security implementation, authentication patterns, authorization, data protection, input validation, encryption, or security best practices"
---

# Security Guidelines

## Authentication

- **JWT**: User-configurable expiry, includes user_id, org_id, roles
- **Magic Links**: 15min expiry, one-time use, Redis storage
- **RBAC**: Owner > Admin > Manager > Employee > ReadOnly

```rust
pub fn generate_jwt(user_id: Uuid, org_id: Uuid, roles: Vec<String>, duration_hours: i64) -> Result<String, JwtError> {
    let exp = (Utc::now() + Duration::hours(duration_hours)).timestamp() as usize;
    // ... JWT generation with configurable expiry
}
```

## Data Security

- **Multi-tenant isolation**: `business_{org_id}` schemas
- **Encryption**: AES-256-GCM with random nonces
- **Input validation**: Validate, sanitize, parameterized queries
- **Rate limiting**: 10 req/sec, 20 burst
- **CORS**: Restrict origins, credentials allowed

```rust
// Schema isolation with format!() for dynamic names
let schema_name = format!("business_{}", org_id);
let query = format!("SELECT * FROM {}.business_data WHERE id = $1", schema_name);

// Secure encryption with random nonces
use rand::RngCore;
let mut nonce_bytes = [0u8; 12];
rand::thread_rng().fill_bytes(&mut nonce_bytes);
```

## WebSocket & Frontend Security

- **WebSocket auth**: JWT token in query params, role-based message permissions
- **CSP**: Restrict script sources, allow SolidJS unsafe-inline
- **XSS prevention**: DOMPurify sanitization, input validation
- **Secure storage**: Tauri encrypted store vs localStorage with base64

```typescript
// Secure token storage
if (PlatformAdapter.isTauri()) {
  const store = new Store(".credentials.dat");
  await store.set("auth_token", token);
} else {
  localStorage.setItem("auth_token", btoa(token));
}
```
