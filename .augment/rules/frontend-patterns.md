---
type: "agent_requested"
description: "SolidJS patterns for the user application (web/Tauri), not the marketing site - includes component patterns, state management, cross-platform UI development, Tauri integration, or frontend performance optimization"
---

# Frontend Patterns

## SolidJS Components

```typescript
interface Props {
  readonly data: BusinessData;
  readonly onUpdate?: (data: BusinessData) => void;
  readonly loading?: boolean;
}

const BusinessComponent: Component<Props> = (props) => {
  const [localState, setLocalState] = createSignal("");
  const computedValue = createMemo(() => props.data.name.toUpperCase());

  createEffect(() => console.log("Data changed:", props.data));

  return (
    <div class="business-component">
      <Show when={!props.loading} fallback={<LoadingSpinner />}>
        <h2>{computedValue()}</h2>
        <input
          value={localState()}
          onInput={(e) => setLocalState(e.currentTarget.value)}
        />
      </Show>
    </div>
  );
};
```

## Store Patterns

```typescript
const [businessStore, setBusinessStore] = createStore({
  profile: null as BusinessProfile | null,
  modules: [] as BusinessModule[],
  loading: false,
});

const businessActions = {
  async loadProfile(id: string) {
    setBusinessStore("loading", true);
    try {
      const profile = await ApiClient.getBusinessProfile(id);
      setBusinessStore({ profile, loading: false });
    } catch (error) {
      setBusinessStore({ loading: false });
    }
  },

  updateProfile(updates: Partial<BusinessProfile>) {
    setBusinessStore("profile", (prev) => ({ ...prev, ...updates }));
  },
};
```

## Resources

```typescript
const createBusinessResource = (businessId: Accessor<string>) => {
  return createResource(businessId, async (id: string) => {
    if (!id) return null;
    return ApiClient.getBusinessProfile(id);
  });
};

const BusinessProfile: Component = () => {
  const params = useParams();
  const [business, { mutate, refetch }] = createBusinessResource(
    () => params.id
  );

  const handleUpdate = async (updates: Partial<BusinessProfile>) => {
    mutate((prev) => (prev ? { ...prev, ...updates } : null));
    try {
      await ApiClient.updateBusinessProfile(params.id, updates);
      refetch();
    } catch (error) {
      refetch();
    }
  };

  return (
    <Show when={business()} fallback={<LoadingSpinner />}>
      {(data) => <BusinessForm business={data()} onUpdate={handleUpdate} />}
    </Show>
  );
};
```

## Cross-Platform

```typescript
class PlatformAdapter {
  static isWeb() {
    return !window.__TAURI__;
  }
  static isTauri() {
    return !!window.__TAURI__;
  }

  static async showNotification(title: string, body: string) {
    if (this.isTauri()) {
      const { sendNotification } = await import("@tauri-apps/api/notification");
      await sendNotification({ title, body });
    } else {
      new Notification(title, { body });
    }
  }
}

class ApiClient {
  static async request<T>(endpoint: string, options = {}): Promise<T> {
    return PlatformAdapter.isTauri()
      ? this.tauriRequest(endpoint, options)
      : this.webRequest(endpoint, options);
  }

  private static async webRequest<T>(endpoint: string, options): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: options.method || "GET",
      headers: { "Content-Type": "application/json", ...options.headers },
      body: options.body ? JSON.stringify(options.body) : undefined,
    });
    return response.json();
  }

  private static async tauriRequest<T>(endpoint: string, options): Promise<T> {
    const { invoke } = await import("@tauri-apps/api/tauri");
    return invoke("api_request", { endpoint, ...options });
  }
}
```

## UI Components

```typescript
interface ButtonProps {
  variant?: "primary" | "secondary" | "danger";
  size?: "sm" | "md" | "lg";
  loading?: boolean;
  disabled?: boolean;
  children: JSX.Element;
  onClick?: () => void;
}

const Button: Component<ButtonProps> = (props) => {
  const classes = createMemo(() =>
    ["btn", `btn-${props.variant || "primary"}`, `btn-${props.size || "md"}`]
      .filter(Boolean)
      .join(" ")
  );

  return (
    <button
      class={classes()}
      disabled={props.disabled || props.loading}
      onClick={props.onClick}
    >
      <Show when={props.loading}>
        <LoadingSpinner />
      </Show>
      {props.children}
    </button>
  );
};
```

## Forms

```typescript
const createFormValidator = <T extends Record<string, any>>(
  initialValues: T,
  validationRules: ValidationRules<T>
) => {
  const [values, setValues] = createStore<T>(initialValues);
  const [errors, setErrors] = createStore<Partial<Record<keyof T, string>>>({});

  const setValue = (field: keyof T, value: any) => {
    setValues(field, value);
    const rule = validationRules[field];
    setErrors(field, rule ? rule(value) : null);
  };

  const isValid = createMemo(() =>
    Object.values(errors).every((error) => !error)
  );

  return { values, errors, setValue, isValid };
};

const BusinessForm: Component<{ business: BusinessProfile }> = (props) => {
  const form = createFormValidator(
    { name: props.business.name, email: props.business.email },
    {
      name: (value) => (!value ? "Name required" : null),
      email: (value) => (!/\S+@\S+\.\S+/.test(value) ? "Invalid email" : null),
    }
  );

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        console.log(form.values);
      }}
    >
      <input
        value={form.values.name}
        onInput={(e) => form.setValue("name", e.currentTarget.value)}
      />
      <Show when={form.errors.name}>
        <span>{form.errors.name}</span>
      </Show>
      <Button type="submit" disabled={!form.isValid()}>
        Save
      </Button>
    </form>
  );
};
```

## WebSocket

```typescript
const createWebSocketConnection = () => {
  const [connected, setConnected] = createSignal(false);
  const [messages, setMessages] = createSignal<WebSocketMessage[]>([]);

  let ws: WebSocket | null = null;

  const connect = () => {
    const wsUrl = PlatformAdapter.isTauri()
      ? "ws://localhost:8000/ws"
      : `${import.meta.env.VITE_WS_URL}/ws`;
    ws = new WebSocket(wsUrl);

    ws.onopen = () => setConnected(true);
    ws.onmessage = (event) =>
      setMessages((prev) => [...prev, JSON.parse(event.data)]);
    ws.onclose = () => {
      setConnected(false);
      setTimeout(connect, 5000);
    };
  };

  const send = (message: WebSocketMessage) => {
    if (ws?.readyState === WebSocket.OPEN) ws.send(JSON.stringify(message));
  };

  connect();
  onCleanup(() => ws?.close());

  return { connected, messages, send };
};
```

## Performance

```typescript
// Lazy loading
const LazyDashboard = lazy(() => import("./Dashboard"));

// Virtual scrolling
const VirtualList: Component<{
  items: any[];
  itemHeight: number;
  renderItem: Function;
}> = (props) => {
  const [scrollTop, setScrollTop] = createSignal(0);
  const visibleItems = createMemo(() => {
    const start = Math.floor(scrollTop() / props.itemHeight);
    const end = start + Math.ceil(400 / props.itemHeight);
    return props.items.slice(start, end);
  });

  return (
    <div onScroll={(e) => setScrollTop(e.currentTarget.scrollTop)}>
      <For each={visibleItems()}>{props.renderItem}</For>
    </div>
  );
};
```
