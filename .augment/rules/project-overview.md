---
type: "agent_requested"
description: "General project questions, architecture overview, technology stack, business model, or when user asks about Vertoie"
---

# Vertoie

AI-generated business software builder. Creates custom applications through natural language conversations.

## Stack

- Backend: Rust + Axum
- Frontend: SolidJS
- Apps: Tauri + SolidJS
- Database: PostgreSQL multi-schema
- LLM: Groq Qwen3 32B → self-hosted

## Structure

- `core/`: REST API, WebSocket, auth, LLM integration
- `web/`: Main platform, onboarding, billing
- `app/`: Customer apps (desktop/mobile/web)

## Model

- Conversation → AI generates modules
- Multi-tenant SaaS
- Cross-platform deployment
- Version management
