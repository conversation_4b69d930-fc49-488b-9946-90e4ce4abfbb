---
type: "agent_requested"
description: "Questions about system architecture, folder structure, multi-schema database design, WebSocket patterns, cross-platform deployment, or performance optimization"
---

# Architecture

## Structure

```
core/          # Rust API
├── routes/    # Handlers
├── models/    # DB models
├── services/  # Logic
├── websocket/ # WS
└── llm/       # AI

app/           # Customer apps
├── src/       # SolidJS
├── src-tauri/ # Rust
└── web/       # Browser

web/           # Platform
```

## Database

- `vertoie`: Platform data
- `{org_id}`: Business data + versioning
- JSONB schemas
- Git-based app versioning

## WebSocket

- Message routing: `domain.resource.action`
- Channel multiplexing
- Clean topic naming: `org:{org_id}:resource:{resource_id}:action`

## Platform Detection

```typescript
static isWeb() { return !window.__TAURI__; }
static apiCall(endpoint, data) {
  return this.isWeb()
    ? fetch(endpoint, options)
    : invoke('api_call', {endpoint, data});
}
```

## Security

- JWT + magic links
- Multi-tenant isolation
- RBAC
- Rate limiting

## Performance

- Rust: Memory safe, async
- SolidJS: Fine-grained reactivity, small bundles
- Redis caching
- Connection pooling
