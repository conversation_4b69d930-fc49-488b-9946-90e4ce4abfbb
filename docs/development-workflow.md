# Vertoie Development Workflow

## Overview

This document outlines the development workflow for the Vertoie project, including setup, development practices, and automation tools.

## Project Structure

```
vertoie/
├── core/           # Rust backend API
├── web/            # SolidJS web frontend
├── app/            # Tauri desktop application
├── docs/           # Documentation
├── specs/          # Specification documents
└── shared/         # Shared utilities and types
```

## Development Setup

### Prerequisites

- **Rust** (latest stable)
- **Node.js** (v18+)
- **pnpm** (v8+)
- **PostgreSQL** (v14+)
- **direnv** (for environment management)

### Initial Setup

1. **Clone and setup environment:**
   ```bash
   git clone <repository-url>
   cd vertoie
   cp .envrc.local.example .envrc.local
   # Edit .envrc.local with your settings
   direnv allow
   ```

2. **Install dependencies:**
   ```bash
   pnpm install
   ```

3. **Setup database:**
   ```bash
   pnpm db:setup
   ```

## Development Commands

### Global Commands (from root)

- `pnpm dev` - Start all development servers
- `pnpm dev:core` - Start Rust backend only
- `pnpm dev:web` - Start web frontend only
- `pnpm dev:app` - Start Tauri app only

### Build Commands

- `pnpm build` - Build all components
- `pnpm build:core` - Build Rust backend
- `pnpm build:web` - Build web frontend
- `pnpm build:app` - Build Tauri app

### Testing Commands

- `pnpm test` - Run all tests
- `pnpm test:core` - Run Rust tests
- `pnpm test:web` - Run web frontend tests
- `pnpm test:app` - Run Tauri app tests

### Code Quality Commands

- `pnpm lint` - Lint all code
- `pnpm format` - Format all code
- `pnpm type-check` - TypeScript type checking

## Development Workflow

### 1. Feature Development

1. **Create feature branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Start development servers:**
   ```bash
   pnpm dev
   ```

3. **Make changes and test:**
   - Backend: http://localhost:8000
   - Web frontend: http://localhost:3000
   - Desktop app: Tauri window

### 2. Code Quality

The project uses automated code quality tools:

- **ESLint** - TypeScript/SolidJS linting
- **Prettier** - Code formatting
- **Clippy** - Rust linting
- **rustfmt** - Rust formatting

### 3. Pre-commit Hooks

Pre-commit hooks automatically run:
- Format code (Prettier, rustfmt)
- Lint code (ESLint, Clippy)
- Type checking

### 4. Testing

- **Rust**: Unit and integration tests with `cargo test`
- **Frontend**: Component tests with Vitest and SolidJS Testing Library
- **Coverage**: Available via `pnpm test --coverage`

## Hot Reload Setup

### Backend (Rust)
- Uses `cargo-watch` for automatic recompilation
- Restarts on file changes in `src/`

### Web Frontend
- Vite dev server with HMR
- Instant updates on save

### Desktop App
- Tauri dev mode with hot reload
- Frontend changes update instantly
- Rust changes trigger rebuild

## IDE Configuration

### VS Code (Recommended)

Install extensions:
- Rust Analyzer
- Tauri
- SolidJS
- ESLint
- Prettier

### Settings

Create `.vscode/settings.json`:
```json
{
  "rust-analyzer.cargo.features": "all",
  "eslint.experimental.useFlatConfig": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

## Troubleshooting

### Common Issues

1. **Database connection errors:**
   - Check PostgreSQL is running
   - Verify DATABASE_URL in .envrc.local

2. **Port conflicts:**
   - Backend: 8000
   - Web: 3000
   - Tauri: Dynamic

3. **Build failures:**
   - Run `pnpm clean` to clear caches
   - Check Rust toolchain version

### Performance Tips

- Use `pnpm dev:core` if only working on backend
- Use `pnpm dev:web` if only working on web frontend
- Close unused development servers to save resources

## Contributing

1. Follow the spec-driven development process
2. Write tests for new features
3. Ensure all quality checks pass
4. Update documentation as needed

## Additional Resources

- [Backend Architecture](../reference/BackendArchitecture.md)
- [Environment Setup](./environment.md)
- [Testing Guidelines](../.augment/rules/testing-guidelines.md)
