import { render, screen } from '@solidjs/testing-library';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import App from './App';

// Mock Tauri API
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn(),
}));

describe('App Component', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('renders welcome message', () => {
    render(() => <App />);
    
    expect(screen.getByText('Welcome to Tauri + Solid')).toBeInTheDocument();
  });

  it('renders logos and links', () => {
    render(() => <App />);
    
    expect(screen.getByAltText('Vite logo')).toBeInTheDocument();
    expect(screen.getByAltText('Tauri logo')).toBeInTheDocument();
    expect(screen.getByAltText('Solid logo')).toBeInTheDocument();
  });

  it('renders greet form', () => {
    render(() => <App />);
    
    expect(screen.getByPlaceholderText('Enter a name...')).toBeInTheDocument();
    expect(screen.getByText('Greet')).toBeInTheDocument();
  });

  it('renders instruction text', () => {
    render(() => <App />);
    
    expect(screen.getByText('Click on the Tauri, Vite, and Solid logos to learn more.')).toBeInTheDocument();
  });
});
