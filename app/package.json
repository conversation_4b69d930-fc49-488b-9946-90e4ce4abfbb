{"name": "app", "version": "0.1.0", "description": "", "type": "module", "scripts": {"start": "vite", "dev": "vite", "build": "vite build", "serve": "vite preview", "tauri": "tauri", "lint": "eslint src", "lint:fix": "eslint src --fix", "format": "prettier --write src", "type-check": "tsc --noEmit", "test": "vitest"}, "license": "MIT", "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "solid-js": "^1.9.3"}, "devDependencies": {"@eslint/js": "^9.32.0", "@solidjs/testing-library": "^0.8.10", "@tauri-apps/cli": "^2", "@testing-library/jest-dom": "^6.6.3", "@vitest/ui": "^3.2.4", "eslint": "^9.32.0", "eslint-plugin-solid": "^0.14.5", "globals": "^16.3.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "typescript": "~5.6.2", "typescript-eslint": "^8.38.0", "vite": "^6.0.3", "vite-plugin-solid": "^2.11.0", "vitest": "^3.2.4"}}