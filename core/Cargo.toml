[package]
name = "vertoie-core"
version = "0.1.0"
edition = "2021"
description = "Vertoie backend API server"
authors = ["Vertoie Team"]
license = "MIT"

[dependencies]
# Web framework
axum = { version = "0.7", features = ["ws", "macros"] }
tower = { version = "0.4", features = ["util"] }
tower-http = { version = "0.5", features = ["cors", "trace"] }

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Database
sqlx = { version = "0.7", features = [
  "runtime-tokio-rustls",
  "postgres",
  "uuid",
  "chrono",
  "json",
  "migrate",
] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
url = "2.5"

# HTTP client for LLM integration
reqwest = { version = "0.12", features = ["json", "stream"] }
futures = "0.3"
async-stream = "0.3"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Environment
dotenvy = "0.15"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

[dev-dependencies]
# Testing
tokio-test = "0.4"

[profile.dev]
# Faster compilation in development
opt-level = 0
debug = true

[profile.release]
# Optimized release builds
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
