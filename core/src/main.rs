use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde_json::{json, Value};
use sqlx::PgPool;
use std::net::SocketAddr;
use tower::ServiceBuilder;
use tower_http::{cors::CorsLayer, trace::TraceLayer};
use tracing::{info, instrument};

mod database;
mod llm;

#[derive(Clone, Debug)]
pub struct AppState {
    pub db_pool: PgPool,
    pub llm_router: llm::<PERSON><PERSON>out<PERSON>,
    pub conversation_manager: std::sync::Arc<tokio::sync::Mutex<llm::ConversationManager>>,
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt()
        .with_env_filter(
            std::env::var("RUST_LOG")
                .unwrap_or_else(|_| "vertoie_core=debug,tower_http=debug".into()),
        )
        .init();

    info!("Starting Vertoie Core API server");

    // Initialize database connection
    let db_config = database::DatabaseConfig::default();
    let db_pool = database::create_pool(db_config).await?;

    // Run database migrations
    sqlx::migrate!("./migrations").run(&db_pool).await?;
    info!("Database migrations completed");

    // Initialize LLM components
    let groq_api_key = std::env::var("GROQ_API_KEY").ok();
    let llm_router = llm::LLMRouter::new(groq_api_key);
    let conversation_manager =
        std::sync::Arc::new(tokio::sync::Mutex::new(llm::ConversationManager::new()));

    // Create application state
    let state = AppState {
        db_pool,
        llm_router,
        conversation_manager,
    };

    // Build our application with routes
    let app = create_app(state);

    // Get server configuration from environment
    let host = std::env::var("SERVER_HOST").unwrap_or_else(|_| "127.0.0.1".to_string());
    let port = std::env::var("SERVER_PORT")
        .unwrap_or_else(|_| "8000".to_string())
        .parse::<u16>()
        .expect("SERVER_PORT must be a valid port number");

    let addr = SocketAddr::new(host.parse()?, port);
    info!("Server listening on {}", addr);

    // Start the server
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

fn create_app(state: AppState) -> Router {
    Router::new()
        .route("/", get(root_handler))
        .route("/health", get(health_handler))
        .route("/api/v1/status", get(status_handler))
        .route("/api/v1/conversations", post(create_conversation))
        .route("/api/v1/conversations/:id/messages", post(add_message))
        .route("/api/v1/conversations/:id", get(get_conversation))
        .route("/api/v1/generate", post(generate_response))
        .with_state(state)
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(CorsLayer::permissive()), // Configure CORS as needed
        )
}

#[instrument]
async fn root_handler() -> Json<Value> {
    Json(json!({
        "name": "Vertoie Core API",
        "version": env!("CARGO_PKG_VERSION"),
        "description": "AI-powered business application platform backend",
        "status": "running"
    }))
}

#[instrument(skip(state))]
async fn health_handler(State(state): State<AppState>) -> Result<Json<Value>, StatusCode> {
    // Check database health
    let db_health = database::health_check(&state.db_pool).await;
    let (db_status, db_message) = match &db_health {
        Ok(health) => (
            health.status_string().to_string(),
            health.message().map(|s| s.to_string()),
        ),
        Err(e) => ("error".to_string(), Some(e.to_string())),
    };

    let overall_status = if db_status == "ok" {
        "healthy"
    } else {
        "unhealthy"
    };

    let mut checks = json!({
        "server": "ok",
        "database": db_status
    });

    if let Some(msg) = db_message {
        checks["database_message"] = json!(msg);
    }

    Ok(Json(json!({
        "status": overall_status,
        "timestamp": chrono::Utc::now(),
        "version": env!("CARGO_PKG_VERSION"),
        "checks": checks
    })))
}

#[instrument]
async fn status_handler(State(_state): State<AppState>) -> Json<Value> {
    Json(json!({
        "api_version": "v1",
        "server_time": chrono::Utc::now(),
        "environment": std::env::var("RUST_LOG").unwrap_or_else(|_| "unknown".to_string()),
        "features": [
            "health_checks",
            "cors_enabled",
            "tracing_enabled"
            // "database_integration" - will be added in Task 2.2
        ]
    }))
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::{
        body::Body,
        http::{Request, StatusCode},
    };
    use tower::util::ServiceExt;

    async fn create_test_app() -> Router {
        // Use test database URL
        let test_db_url = std::env::var("TEST_DATABASE_URL").unwrap_or_else(|_| {
            "postgres://postgres:postgres@localhost:5432/vertoie_test".to_string()
        });

        let db_config = database::DatabaseConfig {
            url: test_db_url,
            ..database::DatabaseConfig::default()
        };

        let db_pool = database::create_pool(db_config)
            .await
            .expect("Failed to create test database pool");

        // Run migrations on test database
        sqlx::migrate!("./migrations")
            .run(&db_pool)
            .await
            .expect("Failed to run test migrations");

        let llm_router = llm::LLMRouter::new(None);
        let conversation_manager =
            std::sync::Arc::new(tokio::sync::Mutex::new(llm::ConversationManager::new()));

        let state = AppState {
            db_pool,
            llm_router,
            conversation_manager,
        };
        create_app(state)
    }

    #[tokio::test]
    async fn test_root_endpoint() {
        let app = create_test_app().await;

        let response = app
            .oneshot(Request::builder().uri("/").body(Body::empty()).unwrap())
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_health_endpoint() {
        let app = create_test_app().await;

        let response = app
            .oneshot(
                Request::builder()
                    .uri("/health")
                    .body(Body::empty())
                    .unwrap(),
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_status_endpoint() {
        let app = create_test_app().await;

        let response = app
            .oneshot(
                Request::builder()
                    .uri("/api/v1/status")
                    .body(Body::empty())
                    .unwrap(),
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);
    }
}

// LLM API Handlers

#[derive(serde::Deserialize)]
struct CreateConversationRequest {
    user_id: uuid::Uuid,
}

#[derive(serde::Deserialize)]
struct AddMessageRequest {
    content: String,
    role: Option<String>,
}

#[derive(serde::Deserialize)]
struct GenerateRequest {
    conversation_id: uuid::Uuid,
    user_input: String,
}

async fn create_conversation(
    State(state): State<AppState>,
    Json(req): Json<CreateConversationRequest>,
) -> Result<Json<Value>, StatusCode> {
    let mut manager = state.conversation_manager.lock().await;
    let conversation = manager.create_conversation(req.user_id);

    Ok(Json(json!({
        "id": conversation.id,
        "user_id": conversation.user_id,
        "created_at": conversation.created_at,
        "message_count": conversation.messages.len()
    })))
}

async fn add_message(
    State(state): State<AppState>,
    Path(conversation_id): Path<uuid::Uuid>,
    Json(req): Json<AddMessageRequest>,
) -> Result<Json<Value>, StatusCode> {
    let mut manager = state.conversation_manager.lock().await;

    let role = match req.role.as_deref() {
        Some("user") => llm::MessageRole::User,
        Some("assistant") => llm::MessageRole::Assistant,
        Some("system") => llm::MessageRole::System,
        _ => llm::MessageRole::User,
    };

    match manager.add_message(conversation_id, role, req.content, None) {
        Ok(message) => Ok(Json(json!({
            "id": message.id,
            "role": message.role,
            "content": message.content,
            "timestamp": message.timestamp
        }))),
        Err(_) => Err(StatusCode::NOT_FOUND),
    }
}

async fn get_conversation(
    State(state): State<AppState>,
    Path(conversation_id): Path<uuid::Uuid>,
) -> Result<Json<Value>, StatusCode> {
    let manager = state.conversation_manager.lock().await;

    match manager.get_conversation(conversation_id) {
        Some(conversation) => Ok(Json(json!({
            "id": conversation.id,
            "user_id": conversation.user_id,
            "messages": conversation.messages,
            "created_at": conversation.created_at,
            "updated_at": conversation.updated_at
        }))),
        None => Err(StatusCode::NOT_FOUND),
    }
}

async fn generate_response(
    State(state): State<AppState>,
    Json(req): Json<GenerateRequest>,
) -> Result<Json<Value>, StatusCode> {
    let manager = state.conversation_manager.lock().await;

    let conversation = match manager.get_conversation(req.conversation_id) {
        Some(conv) => conv.clone(),
        None => return Err(StatusCode::NOT_FOUND),
    };

    drop(manager); // Release the lock before async operation

    match state
        .llm_router
        .generate_business_model(&conversation, &req.user_input)
        .await
    {
        Ok(response) => Ok(Json(json!({
            "id": response.id,
            "conversation_id": response.conversation_id,
            "content": response.content,
            "tokens_used": response.tokens_used,
            "generation_time_ms": response.generation_time_ms,
            "provider": response.provider
        }))),
        Err(e) => {
            tracing::error!("LLM generation failed: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}
