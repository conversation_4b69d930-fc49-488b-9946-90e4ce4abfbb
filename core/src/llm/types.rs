use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use thiserror::Error;
use uuid::Uuid;

/// LLM-related errors
#[derive(Debug, Error)]
pub enum LLMError {
    #[error("API request failed: {0}")]
    ApiError(String),
    
    #[error("Rate limited by provider")]
    RateLimited,
    
    #[error("Invalid response format: {0}")]
    InvalidResponse(String),
    
    #[error("Provider unavailable: {0}")]
    ProviderUnavailable(String),
    
    #[error("Authentication failed")]
    AuthenticationFailed,
    
    #[error("Request timeout")]
    Timeout,
    
    #[error("HTTP error: {0}")]
    HttpError(#[from] reqwest::Error),
    
    #[error("JSON parsing error: {0}")]
    JsonError(#[from] serde_json::Error),
}

/// Generation types with different credit costs
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum GenerationType {
    /// Standard generation - 1 credit
    Standard,
    /// Custom generation with specific requirements - 2 credits
    Custom,
}

impl GenerationType {
    pub fn credit_cost(&self) -> u32 {
        match self {
            GenerationType::Standard => 1,
            GenerationType::Custom => 2,
        }
    }
}

/// Business model analysis result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BusinessAnalysis {
    pub id: Uuid,
    pub industry: String,
    pub modules: Vec<String>,
    pub schemas: HashMap<String, serde_json::Value>,
    pub confidence_score: f32,
    pub generated_at: chrono::DateTime<chrono::Utc>,
}

/// LLM conversation message
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationMessage {
    pub id: Uuid,
    pub role: MessageRole,
    pub content: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub metadata: Option<serde_json::Value>,
}

/// Message roles in conversation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageRole {
    #[serde(rename = "user")]
    User,
    #[serde(rename = "assistant")]
    Assistant,
    #[serde(rename = "system")]
    System,
}

/// Conversation context
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Conversation {
    pub id: Uuid,
    pub user_id: Uuid,
    pub messages: Vec<ConversationMessage>,
    pub context: HashMap<String, serde_json::Value>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

/// LLM generation request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenerationRequest {
    pub conversation_id: Uuid,
    pub user_input: String,
    pub generation_type: GenerationType,
    pub context: Option<HashMap<String, serde_json::Value>>,
}

/// LLM generation response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenerationResponse {
    pub id: Uuid,
    pub conversation_id: Uuid,
    pub content: String,
    pub analysis: Option<BusinessAnalysis>,
    pub tokens_used: u32,
    pub generation_time_ms: u64,
    pub provider: String,
}

/// Streaming generation chunk
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenerationChunk {
    pub id: Uuid,
    pub conversation_id: Uuid,
    pub content: String,
    pub is_final: bool,
    pub progress: Option<f32>,
}

/// Industry detection result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IndustryDetection {
    pub industry: String,
    pub confidence: f32,
    pub keywords: Vec<String>,
}

/// Module selection result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModuleSelection {
    pub modules: Vec<String>,
    pub reasoning: String,
    pub priority_order: Vec<String>,
}
