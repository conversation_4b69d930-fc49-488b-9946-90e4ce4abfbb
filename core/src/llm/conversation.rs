use crate::llm::types::*;
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};

/// Conversation manager for handling LLM conversations
#[derive(Debug)]
pub struct ConversationManager {
    conversations: HashMap<Uuid, Conversation>,
    max_messages_per_conversation: usize,
    max_conversation_age_hours: i64,
}

impl ConversationManager {
    /// Create a new conversation manager
    pub fn new() -> Self {
        Self {
            conversations: HashMap::new(),
            max_messages_per_conversation: 100,
            max_conversation_age_hours: 24,
        }
    }

    /// Create a new conversation
    pub fn create_conversation(&mut self, user_id: Uuid) -> Conversation {
        let conversation = Conversation {
            id: Uuid::new_v4(),
            user_id,
            messages: Vec::new(),
            context: HashMap::new(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        self.conversations.insert(conversation.id, conversation.clone());
        conversation
    }

    /// Get a conversation by ID
    pub fn get_conversation(&self, conversation_id: Uuid) -> Option<&Conversation> {
        self.conversations.get(&conversation_id)
    }

    /// Get a mutable conversation by ID
    pub fn get_conversation_mut(&mut self, conversation_id: Uuid) -> Option<&mut Conversation> {
        self.conversations.get_mut(&conversation_id)
    }

    /// Add a message to a conversation
    pub fn add_message(
        &mut self,
        conversation_id: Uuid,
        role: MessageRole,
        content: String,
        metadata: Option<serde_json::Value>,
    ) -> Result<ConversationMessage, LLMError> {
        let conversation = self
            .conversations
            .get_mut(&conversation_id)
            .ok_or_else(|| LLMError::InvalidResponse("Conversation not found".to_string()))?;

        let message = ConversationMessage {
            id: Uuid::new_v4(),
            role,
            content,
            timestamp: Utc::now(),
            metadata,
        };

        conversation.messages.push(message.clone());
        conversation.updated_at = Utc::now();

        // Trim conversation if it gets too long
        self.trim_conversation(conversation_id)?;

        Ok(message)
    }

    /// Update conversation context
    pub fn update_context(
        &mut self,
        conversation_id: Uuid,
        key: String,
        value: serde_json::Value,
    ) -> Result<(), LLMError> {
        let conversation = self
            .conversations
            .get_mut(&conversation_id)
            .ok_or_else(|| LLMError::InvalidResponse("Conversation not found".to_string()))?;

        conversation.context.insert(key, value);
        conversation.updated_at = Utc::now();

        Ok(())
    }

    /// Get conversation context value
    pub fn get_context(
        &self,
        conversation_id: Uuid,
        key: &str,
    ) -> Option<&serde_json::Value> {
        self.conversations
            .get(&conversation_id)?
            .context
            .get(key)
    }

    /// Get all conversations for a user
    pub fn get_user_conversations(&self, user_id: Uuid) -> Vec<&Conversation> {
        self.conversations
            .values()
            .filter(|conv| conv.user_id == user_id)
            .collect()
    }

    /// Delete a conversation
    pub fn delete_conversation(&mut self, conversation_id: Uuid) -> bool {
        self.conversations.remove(&conversation_id).is_some()
    }

    /// Clean up old conversations
    pub fn cleanup_old_conversations(&mut self) -> usize {
        let cutoff_time = Utc::now() - chrono::Duration::hours(self.max_conversation_age_hours);
        let initial_count = self.conversations.len();

        self.conversations.retain(|_, conv| conv.updated_at > cutoff_time);

        initial_count - self.conversations.len()
    }

    /// Trim conversation messages if too long
    fn trim_conversation(&mut self, conversation_id: Uuid) -> Result<(), LLMError> {
        let conversation = self
            .conversations
            .get_mut(&conversation_id)
            .ok_or_else(|| LLMError::InvalidResponse("Conversation not found".to_string()))?;

        if conversation.messages.len() > self.max_messages_per_conversation {
            // Keep the first message (usually system prompt) and the most recent messages
            let mut trimmed_messages = Vec::new();
            
            // Keep first message if it's a system message
            if let Some(first_msg) = conversation.messages.first() {
                if matches!(first_msg.role, MessageRole::System) {
                    trimmed_messages.push(first_msg.clone());
                }
            }

            // Keep the most recent messages
            let keep_count = self.max_messages_per_conversation - trimmed_messages.len();
            let start_index = conversation.messages.len().saturating_sub(keep_count);
            
            for msg in conversation.messages.iter().skip(start_index) {
                if !matches!(msg.role, MessageRole::System) || trimmed_messages.is_empty() {
                    trimmed_messages.push(msg.clone());
                }
            }

            conversation.messages = trimmed_messages;
        }

        Ok(())
    }

    /// Get conversation summary for display
    pub fn get_conversation_summary(&self, conversation_id: Uuid) -> Option<ConversationSummary> {
        let conversation = self.conversations.get(&conversation_id)?;
        
        let last_message = conversation.messages.last()?.content.clone();
        let message_count = conversation.messages.len();
        
        // Get first user message as title
        let title = conversation
            .messages
            .iter()
            .find(|msg| matches!(msg.role, MessageRole::User))
            .map(|msg| {
                let content = &msg.content;
                if content.len() > 50 {
                    format!("{}...", &content[..47])
                } else {
                    content.clone()
                }
            })
            .unwrap_or_else(|| "New Conversation".to_string());

        Some(ConversationSummary {
            id: conversation.id,
            user_id: conversation.user_id,
            title,
            last_message,
            message_count,
            created_at: conversation.created_at,
            updated_at: conversation.updated_at,
        })
    }

    /// Get statistics about conversations
    pub fn get_stats(&self) -> ConversationStats {
        let total_conversations = self.conversations.len();
        let total_messages: usize = self.conversations.values()
            .map(|conv| conv.messages.len())
            .sum();
        
        let active_conversations = self.conversations.values()
            .filter(|conv| {
                let one_hour_ago = Utc::now() - chrono::Duration::hours(1);
                conv.updated_at > one_hour_ago
            })
            .count();

        ConversationStats {
            total_conversations,
            total_messages,
            active_conversations,
        }
    }
}

/// Conversation summary for listing
#[derive(Debug, Clone, serde::Serialize)]
pub struct ConversationSummary {
    pub id: Uuid,
    pub user_id: Uuid,
    pub title: String,
    pub last_message: String,
    pub message_count: usize,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// Conversation statistics
#[derive(Debug, serde::Serialize)]
pub struct ConversationStats {
    pub total_conversations: usize,
    pub total_messages: usize,
    pub active_conversations: usize,
}

impl Default for ConversationManager {
    fn default() -> Self {
        Self::new()
    }
}
