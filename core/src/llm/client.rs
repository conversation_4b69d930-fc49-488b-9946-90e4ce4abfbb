use crate::llm::types::*;
use async_stream::stream;
use futures::Stream;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::pin::Pin;
use std::time::Instant;
use uuid::Uuid;

/// Groq API client for LLM interactions
#[derive(Debug, Clone)]
pub struct GroqClient {
    client: Client,
    api_key: String,
    base_url: String,
    model: String,
}

/// Groq API request format
#[derive(Debug, Serialize)]
struct GroqRequest {
    model: String,
    messages: Vec<GroqMessage>,
    temperature: f32,
    max_tokens: Option<u32>,
    stream: bool,
}

/// Groq message format
#[derive(Debug, Serialize, Deserialize)]
struct GroqMessage {
    role: String,
    content: String,
}

/// Groq API response format
#[derive(Debug, Deserialize)]
struct GroqResponse {
    id: String,
    choices: Vec<GroqChoice>,
    usage: Option<GroqUsage>,
}

/// Groq choice in response
#[derive(Debug, Deserialize)]
struct GroqChoice {
    message: GroqMessage,
    finish_reason: Option<String>,
}

/// Groq usage statistics
#[derive(Debug, Deserialize)]
struct GroqUsage {
    prompt_tokens: u32,
    completion_tokens: u32,
    total_tokens: u32,
}

impl GroqClient {
    /// Create a new Groq client
    pub fn new(api_key: String) -> Self {
        Self {
            client: Client::new(),
            api_key,
            base_url: "https://api.groq.com/openai/v1".to_string(),
            model: "llama-3.1-70b-versatile".to_string(), // Using Llama 3.1 70B as default
        }
    }

    /// Generate a response for the given conversation
    pub async fn generate(
        &self,
        conversation: &Conversation,
        user_input: &str,
    ) -> Result<GenerationResponse, LLMError> {
        let start_time = Instant::now();
        
        // Build messages from conversation history
        let mut messages = vec![
            GroqMessage {
                role: "system".to_string(),
                content: self.build_system_prompt(),
            }
        ];

        // Add conversation history
        for msg in &conversation.messages {
            messages.push(GroqMessage {
                role: match msg.role {
                    MessageRole::User => "user".to_string(),
                    MessageRole::Assistant => "assistant".to_string(),
                    MessageRole::System => "system".to_string(),
                },
                content: msg.content.clone(),
            });
        }

        // Add current user input
        messages.push(GroqMessage {
            role: "user".to_string(),
            content: user_input.to_string(),
        });

        let request = GroqRequest {
            model: self.model.clone(),
            messages,
            temperature: 0.7,
            max_tokens: Some(4000),
            stream: false,
        };

        let response = self
            .client
            .post(&format!("{}/chat/completions", self.base_url))
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&request)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(LLMError::ApiError(format!(
                "Groq API error: {}",
                error_text
            )));
        }

        let groq_response: GroqResponse = response.json().await?;
        
        let content = groq_response
            .choices
            .first()
            .ok_or_else(|| LLMError::InvalidResponse("No choices in response".to_string()))?
            .message
            .content
            .clone();

        let tokens_used = groq_response
            .usage
            .map(|u| u.total_tokens)
            .unwrap_or(0);

        let generation_time = start_time.elapsed().as_millis() as u64;

        Ok(GenerationResponse {
            id: Uuid::new_v4(),
            conversation_id: conversation.id,
            content,
            analysis: None, // Will be populated by analysis pipeline
            tokens_used,
            generation_time_ms: generation_time,
            provider: "groq".to_string(),
        })
    }

    /// Generate a streaming response
    pub async fn generate_stream(
        &self,
        conversation: &Conversation,
        user_input: &str,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<GenerationChunk, LLMError>> + Send>>, LLMError> {
        // For now, return a simple stream that simulates streaming
        // In a real implementation, this would use Groq's streaming API
        let conversation_id = conversation.id;
        let content = self.generate(conversation, user_input).await?.content;
        
        let stream = stream! {
            let words: Vec<&str> = content.split_whitespace().collect();
            let chunk_size = 5; // Words per chunk
            
            for (i, chunk) in words.chunks(chunk_size).enumerate() {
                let chunk_content = chunk.join(" ");
                let is_final = i == (words.len() + chunk_size - 1) / chunk_size - 1;
                let progress = (i + 1) as f32 / ((words.len() + chunk_size - 1) / chunk_size) as f32;
                
                yield Ok(GenerationChunk {
                    id: Uuid::new_v4(),
                    conversation_id,
                    content: chunk_content,
                    is_final,
                    progress: Some(progress),
                });
                
                // Simulate streaming delay
                tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            }
        };

        Ok(Box::pin(stream))
    }

    /// Build the system prompt for business model generation
    fn build_system_prompt(&self) -> String {
        r#"You are Vertoie, an AI assistant that helps users create custom business applications through conversation.

Your role is to:
1. Understand the user's business needs and requirements
2. Identify the industry and business model type
3. Suggest appropriate application modules and features
4. Generate database schemas and API structures
5. Provide implementation guidance

Always respond in a helpful, professional manner and ask clarifying questions when needed.
Focus on practical, implementable solutions that can be built with modern web technologies.

When analyzing a business, consider:
- Target market and user personas
- Core business processes and workflows
- Data requirements and relationships
- Integration needs with external services
- Scalability and performance requirements

Provide structured responses that can be easily parsed and implemented."#.to_string()
    }
}

impl Default for GroqClient {
    fn default() -> Self {
        Self::new("".to_string())
    }
}
