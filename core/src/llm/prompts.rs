use std::collections::HashMap;

/// Prompt builder for creating context-aware prompts
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct PromptBuilder {
    universal_patterns: String,
    industry_context: HashMap<String, String>,
    module_templates: HashMap<String, String>,
}

impl PromptBuilder {
    /// Create a new prompt builder with default patterns
    pub fn new() -> Self {
        let mut builder = Self {
            universal_patterns: Self::default_universal_patterns(),
            industry_context: HashMap::new(),
            module_templates: HashMap::new(),
        };

        builder.load_industry_contexts();
        builder.load_module_templates();
        builder
    }

    /// Build a prompt for business model generation
    pub fn build_business_prompt(
        &self,
        description: &str,
        industry: Option<&str>,
        context: Option<&HashMap<String, serde_json::Value>>,
    ) -> String {
        let mut prompt = self.universal_patterns.clone();

        // Add industry-specific context
        if let Some(industry) = industry {
            if let Some(industry_context) = self.industry_context.get(industry) {
                prompt.push_str("\n\n");
                prompt.push_str(industry_context);
            }
        }

        // Add conversation context if available
        if let Some(context) = context {
            if let Some(previous_analysis) = context.get("previous_analysis") {
                prompt.push_str("\n\nPrevious Analysis Context:\n");
                prompt.push_str(&previous_analysis.to_string());
            }
        }

        // Add the business description
        prompt.push_str(&format!("\n\nBusiness Description: {}", description));

        // Add generation instructions
        prompt.push_str(self.get_generation_instructions());

        prompt
    }

    /// Build a prompt for module selection
    pub fn build_module_prompt(
        &self,
        business_analysis: &str,
        available_modules: &[String],
    ) -> String {
        let mut prompt = String::from(
            "Based on the following business analysis, select the most appropriate modules:\n\n",
        );

        prompt.push_str("Business Analysis:\n");
        prompt.push_str(business_analysis);

        prompt.push_str("\n\nAvailable Modules:\n");
        for (i, module) in available_modules.iter().enumerate() {
            prompt.push_str(&format!("{}. {}\n", i + 1, module));
        }

        prompt.push_str("\n\nPlease select 3-7 modules that would be most beneficial for this business and explain your reasoning.");

        prompt
    }

    /// Build a prompt for schema generation
    pub fn build_schema_prompt(
        &self,
        business_analysis: &str,
        selected_modules: &[String],
    ) -> String {
        let mut prompt =
            String::from("Generate database schemas for the following business application:\n\n");

        prompt.push_str("Business Analysis:\n");
        prompt.push_str(business_analysis);

        prompt.push_str("\n\nSelected Modules:\n");
        for module in selected_modules {
            prompt.push_str(&format!("- {}\n", module));
        }

        prompt.push_str("\n\nGenerate PostgreSQL schemas with proper relationships, indexes, and constraints. Include JSON schemas for API responses.");

        prompt
    }

    /// Get default universal patterns for business analysis
    fn default_universal_patterns() -> String {
        r#"You are Vertoie, an AI assistant specialized in analyzing business requirements and generating custom application architectures.

Your expertise includes:
- Business model analysis and validation
- Database schema design
- API architecture planning
- Module selection and integration
- Industry best practices

When analyzing a business, consider:
1. Target market and customer segments
2. Core value proposition and revenue streams
3. Key business processes and workflows
4. Data requirements and relationships
5. Integration needs and scalability requirements
6. Compliance and security considerations

Provide structured, actionable recommendations that can be implemented using modern web technologies."#.to_string()
    }

    /// Load industry-specific context patterns
    fn load_industry_contexts(&mut self) {
        self.industry_context.insert(
            "e-commerce".to_string(),
            r#"E-commerce Industry Context:
- Focus on product catalog management, inventory tracking, and order processing
- Consider payment gateway integration, shipping logistics, and customer reviews
- Important modules: Product Management, Order Management, Payment Processing, Inventory Control
- Key metrics: Conversion rates, average order value, customer lifetime value"#
                .to_string(),
        );

        self.industry_context.insert(
            "saas".to_string(),
            r#"SaaS Industry Context:
- Emphasize user management, subscription billing, and feature access control
- Consider multi-tenancy, API rate limiting, and usage analytics
- Important modules: User Management, Billing & Subscriptions, Analytics, API Management
- Key metrics: Monthly recurring revenue, churn rate, customer acquisition cost"#
                .to_string(),
        );

        self.industry_context.insert(
            "healthcare".to_string(),
            r#"Healthcare Industry Context:
- Prioritize HIPAA compliance, patient data security, and audit trails
- Consider appointment scheduling, medical records, and provider management
- Important modules: Patient Management, Appointment Scheduling, Medical Records, Compliance
- Key requirements: Data encryption, access controls, audit logging"#
                .to_string(),
        );

        self.industry_context.insert(
            "education".to_string(),
            r#"Education Industry Context:
- Focus on student information systems, course management, and assessment tools
- Consider gradebooks, attendance tracking, and parent communication
- Important modules: Student Management, Course Management, Assessment Tools, Communication
- Key features: Role-based access, progress tracking, reporting"#
                .to_string(),
        );
    }

    /// Load module-specific templates
    fn load_module_templates(&mut self) {
        self.module_templates.insert(
            "user_management".to_string(),
            "User authentication, authorization, profile management, and role-based access control"
                .to_string(),
        );

        self.module_templates.insert(
            "product_catalog".to_string(),
            "Product information management, categorization, pricing, and inventory tracking"
                .to_string(),
        );

        self.module_templates.insert(
            "order_management".to_string(),
            "Order processing, payment handling, fulfillment tracking, and customer communication"
                .to_string(),
        );

        self.module_templates.insert(
            "analytics".to_string(),
            "Data collection, reporting, dashboards, and business intelligence tools".to_string(),
        );

        self.module_templates.insert(
            "communication".to_string(),
            "Messaging, notifications, email campaigns, and customer support tools".to_string(),
        );
    }

    /// Get generation instructions for structured output
    fn get_generation_instructions(&self) -> &str {
        r#"

Please provide your analysis in the following JSON format:
{
  "industry": "detected industry category",
  "business_model": "identified business model type",
  "target_market": "primary customer segments",
  "value_proposition": "core value offering",
  "key_features": ["list", "of", "essential", "features"],
  "recommended_modules": ["module1", "module2", "module3"],
  "data_requirements": {
    "entities": ["primary", "data", "entities"],
    "relationships": "description of key relationships"
  },
  "technical_considerations": {
    "scalability": "scalability requirements",
    "integrations": ["external", "services", "needed"],
    "compliance": "regulatory requirements"
  },
  "implementation_priority": ["high", "medium", "low priority features"]
}"#
    }

    /// Detect industry from business description
    pub fn detect_industry(&self, description: &str) -> Option<String> {
        let description_lower = description.to_lowercase();

        // Simple keyword-based industry detection
        if description_lower.contains("shop")
            || description_lower.contains("store")
            || description_lower.contains("sell")
        {
            Some("e-commerce".to_string())
        } else if description_lower.contains("software")
            || description_lower.contains("saas")
            || description_lower.contains("platform")
        {
            Some("saas".to_string())
        } else if description_lower.contains("health")
            || description_lower.contains("medical")
            || description_lower.contains("patient")
        {
            Some("healthcare".to_string())
        } else if description_lower.contains("school")
            || description_lower.contains("education")
            || description_lower.contains("student")
        {
            Some("education".to_string())
        } else {
            None
        }
    }

    /// Get available modules for an industry
    pub fn get_industry_modules(&self, industry: &str) -> Vec<String> {
        match industry {
            "e-commerce" => vec![
                "user_management".to_string(),
                "product_catalog".to_string(),
                "order_management".to_string(),
                "payment_processing".to_string(),
                "inventory_management".to_string(),
                "analytics".to_string(),
            ],
            "saas" => vec![
                "user_management".to_string(),
                "subscription_billing".to_string(),
                "api_management".to_string(),
                "analytics".to_string(),
                "support_system".to_string(),
            ],
            "healthcare" => vec![
                "patient_management".to_string(),
                "appointment_scheduling".to_string(),
                "medical_records".to_string(),
                "billing_insurance".to_string(),
                "compliance_audit".to_string(),
            ],
            "education" => vec![
                "student_management".to_string(),
                "course_management".to_string(),
                "assessment_tools".to_string(),
                "gradebook".to_string(),
                "communication".to_string(),
            ],
            _ => vec![
                "user_management".to_string(),
                "content_management".to_string(),
                "analytics".to_string(),
                "communication".to_string(),
            ],
        }
    }
}

impl Default for PromptBuilder {
    fn default() -> Self {
        Self::new()
    }
}
