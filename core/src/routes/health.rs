use axum::{http::StatusCode, response::Json};
use serde_json::{json, Value};
use tracing::instrument;

/// Health check endpoint for monitoring and load balancers
#[instrument]
pub async fn health_check() -> Result<Json<Value>, StatusCode> {
    // This will be expanded in Task 2.2 to include database health checks
    Ok(Json(json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now(),
        "version": env!("CARGO_PKG_VERSION"),
        "checks": {
            "server": "ok"
            // Database check will be added in Task 2.2
        }
    })))
}
